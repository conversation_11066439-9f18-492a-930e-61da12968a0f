<template>
  <view class="order-record-page">
    <!-- z-paging组件，铺满全屏 -->
    <z-paging
      ref="pagingRef"
      v-model="orderList"
      :fixed="true"
      :auto="true"
      :refresher-enabled="true"
      :refresher-threshold="80"
      :loading-more-enabled="false"
      @query="queryOrderList"
    >
      <!-- 顶部固定区域：搜索和筛选 -->
      <template #bottom>
        <view class="bottomBtn">
          <view class="cancelBtn">取消订单</view>
          <view class="cancelBtn">删除订单</view>
          <view class="payBtn">立即支付</view>
          <view class="payBtn">申请退款</view>
          <view class="payBtn" @click="refundDetails">退款详情</view>
        </view>
      </template>
      <!-- 订单卡片列表 -->
      <view class="order-card-list">
        <view class="order-card" v-for="item in orderList" :key="item.id">
          <!-- 订单头部 -->
          <view class="order-header">
            <view class="order-type">
              <text class="type-icon"></text>
              <text class="type-text">{{ item.typeName }}</text>
            </view>
            <view class="order-status" :class="getStatusClass(item.status)">
              {{ getStatusText(item.status) }}
            </view>
          </view>

          <!-- 订单标题 -->
          <view class="order-title">{{ item.title }}</view>

          <!-- 订单详情 -->
          <view class="order-details">
            <view class="detail-row">
              <text class="detail-label">缴费时间:</text>
              <text class="detail-value">{{ item.paymentTime }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">缴费明细:</text>
              <text class="detail-value">{{ item.paymentDetail }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">应缴金额:</text>
              <text class="detail-value">{{ item.shouldPayAmount }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">减免金额:</text>
              <text class="detail-value">{{ item.discountAmount }}</text>
            </view>
          </view>

          <!-- 应缴金额 -->
          <view class="actual-amount">
            <text class="amount-label">应缴金额</text>
            <text class="amount-value">¥{{ item.actualAmount }}</text>
          </view>
        </view>

        <view class="payee">
          <view class="payee_order-card">
            <!-- 订单详情 -->
            <view class="payee_order-details">
              <view class="payee_detail-row">
                <text class="payee_detail-label">收款方:</text>
                <text class="payee_detail-value">{{ "dwqwef" }}</text>
              </view>
              <view class="payee_detail-row">
                <text class="payee_detail-label">内部订单编号:</text>
                <text class="payee_detail-value">{{ "3423432423" }}</text>
              </view>
              <view class="payee_detail-row">
                <text class="payee_detail-label">交易流水号:</text>
                <text class="payee_detail-value">{{ "5645645" }}</text>
              </view>
              <view class="payee_detail-row">
                <text class="payee_detail-label">创建时间:</text>
                <text class="payee_detail-value">{{ "456456" }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </z-paging>

    <uni-popup
      type="bottom"
      class="popup"
      ref="popup"
      @change="change"
      @close="close"
    >
      <view class="popup-content">
        <view class="handle-card">
          <view class="handle-card-close" @click="close">
            <uni-icons type="closeempty" size="22" color="#000000"></uni-icons>
          </view>
        </view>
        <view class="card-List">
          <view class="cardItem">
            <view class="cardItem_label">退款原因</view>
            <view class="cardItem_value">取消缴费</view>
          </view>
          <view class="cardItem">
            <view class="cardItem_label">退款金额</view>
            <view class="cardItem_value">¥200.00</view>
          </view>
          <view class="cardItem">
            <view class="cardItem_label">退款编号</view>
            <view class="cardItem_value">31231231</view>
          </view>
          <view class="cardItem">
            <view class="cardItem_label">申请退款时间</view>
            <view class="cardItem_value">31231231</view>
          </view>
          <view class="cardItem">
            <view class="cardItem_label">退款到账时间</view>
            <view class="cardItem_value">31231231</view>
          </view>
          <view class="cardItem">
            <view class="cardItem_label">退款去向</view>
            <view class="cardItem_value">31231231</view>
          </view>
        </view>

        <view class="bottom_btn">
          <view class="bottom_del_btn">删除记录</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";

const popup = ref(null);

const refundDetails = () => {
  popup.value.open();
};
const orderList = ref([
  {
    id: "order_1",
    type: "tuition",
    typeName: "学会费",
    title: "2024下学年学会费",
    paymentTime: "2024.05.12-2025.06.12",
    paymentDetail: "解决订单",
    shouldPayAmount: "1000.00",
    discountAmount: "200.00",
    actualAmount: "800.00",
    status: "unpaid",
  },
]);
const pagingRef = ref(null);

// 查询订单列表
const queryOrderList = async (pageNo, pageSize) => {
  console.log("查询订单列表");

  try {
    const params = {
      pageNo,
      pageSize,
    };

    // 使用z-paging的complete方法完成数据加载
    pagingRef.value.complete(orderList.value);
  } catch (error) {
    console.error("查询订单列表失败:", error);
    pagingRef.value.complete([]);
  }
};

// 获取状态样式类
const getStatusClass = (status) => {
  return status === "paid" ? "status-paid" : "status-unpaid";
};

// 获取状态文本
const getStatusText = (status) => {
  return status === "paid" ? "已支付" : "待支付";
};

// @refresherrefresh 下拉刷新时触发
const onRefresh = () => {
  console.log("下拉刷新,");
  // 可以在这里做一些额外的刷新逻辑
  // 实际的数据刷新会通过@query事件处理
};

// 页面挂载时的处理
onMounted(() => {
  // 页面初始化逻辑
});
</script>

<style scoped>
.order-record-page {
  min-height: 100vh;
  background-color: #f9faf9;
  padding-bottom: env(safe-area-inset-bottom);
}

.bottomBtn {
  background-color: #ffffff;
  height: 100rpx;
  padding: 20rpx;
  border-top: 2rpx solid #ebebeb;
  display: flex;
  gap: 30rpx;
  justify-content: flex-end;
}

.cancelBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 148rpx;
  height: 48rpx;
  border-radius: 10rpx;
  border: 2rpx solid #d1d1d1;
  font-weight: 400;
  font-size: 24rpx;
  color: #d1d1d1;
  background: #ffffff;
}

.payBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 148rpx;
  height: 48rpx;
  border-radius: 10rpx;
  background: #11c685;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
}

.order-card {
  background: #ffffff;
  padding: 32rpx;
  border-bottom: 1rpx solid #ebebeb;
}

.payee_order-card {
  background: #ffffff;
  padding: 32rpx;
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.order-type {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.type-icon {
  width: 6rpx;
  height: 24rpx;
  background: #00b781;
  border-radius: 3rpx;
}

.type-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-paid {
  background-color: #e8f5e8;
  color: #00b781;
}

.status-unpaid {
  background-color: #fff7e6;
  color: #ff8c00;
}

/* 订单详情 */
.order-details {
  margin-bottom: 32rpx;
}

.payee_order-details {
  margin-bottom: 32rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.payee_detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 28rpx;
  color: #666666;
}

.payee_detail-label {
  font-weight: 600;
  font-size: 24rpx;
  color: #000000;
}

.detail-value {
  font-size: 28rpx;
  color: #333333;
  text-align: right;
}

.payee_detail-value {
  font-weight: 600;
  font-size: 24rpx;
  color: #000000;
  text-align: right;
}

/* z-paging组件样式覆盖 */
:deep(.zp-paging-container) {
  background-color: #ffffff;
}

:deep(.zp-paging-container-content) {
  background-color: #ffffff;
}

.popup-content {
  height: 660rpx;
  background: #ffffff;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
}

.handle-card {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.handle-card-close {
  padding: 30rpx;
}

.card-List {
  padding-left: 60rpx;
  padding-right: 60rpx;
}
.cardItem {
  display: flex;
  padding-bottom: 30rpx;
  align-items: center;
  justify-content: space-between;
}

.bottom_btn {
  padding-left: 60rpx;
  padding-right: 60rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.bottom_del_btn {
  background: #ffffff;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #dfdfdf;
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  width: 172rpx;
  height: 70rpx;
}
</style>
