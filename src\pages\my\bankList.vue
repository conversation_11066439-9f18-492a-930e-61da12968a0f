<template>
  <view class="bank-list-page">
    <!-- z-paging组件，开启下拉刷新，不开启上拉加载 -->
    <z-paging
      ref="pagingRef"
      v-model="bankCardList"
      :fixed="true"
      :auto="true"
      :refresher-enabled="true"
      :refresher-threshold="80"
      :loading-more-enabled="false"
      @query="queryBankCardList"
      @refresherrefresh="onRefresh"
    >
      <!-- 银行卡列表 -->
      <view class="bank-card-list">
        <view
          class="bank-card-item"
          v-for="item in bankCardList"
          :key="item.id"
          @click="handleCardClick(item)"
        >
          <!-- 银行卡片 -->
          <view
            class="bank-card"
            :style="{
              backgroundImage: `url(${getBankBackground(item.bankCode)})`,
            }"
          >
            <!-- 银行信息区域 -->
            <view class="bank-info">
              <view class="bank-header">
                <image
                  class="bank-icon"
                  :src="getBankIcon(item.bankCode)"
                  mode="aspectFit"
                />
                <view class="bank-name">{{ getBankName(item.bankCode) }}</view>
              </view>
              <view class="card-type">{{ item.cardType || "储蓄卡" }}</view>
            </view>

            <!-- 卡号信息区域 -->
            <view class="card-info">
              <view class="card-number">
                <text class="card-dots">•••• •••• ••••</text>
                <text class="card-last-four">{{
                  item.cardNumber.slice(-4)
                }}</text>
              </view>
              <view class="card-actions">
                <view class="unbind-btn" @click.stop="handleUnbind(item)">
                  解绑
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="bankCardList.length === 0 && !loading" class="empty-state">
          <image
            class="empty-icon"
            src="@/static/image/yhkIcon.png"
            mode="aspectFit"
          />
          <text class="empty-text">暂无绑定的银行卡</text>
          <text class="empty-tip">请点击下方按钮添加银行卡</text>
        </view>
      </view>

      <!-- 底部固定区域：操作按钮 -->
      <template #bottom>
        <view class="bottom-section">
          <view class="button-row">
            <view class="action-btn secondary" @click="handleSetPayPassword">
              设置支付密码
            </view>
            <view class="action-btn primary" @click="handleAddBank">
              添加银行卡
            </view>
          </view>
        </view>
      </template>
    </z-paging>

    <!-- 支付密码验证弹窗 -->
    <PayPasswordVerify
      v-model:visible="showPasswordVerify"
      :verify-function="verifyPayPassword"
      @success="onPasswordVerifySuccess"
      @failed="onPasswordVerifyFailed"
      @cancel="onPasswordVerifyCancel"
    />
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import PayPasswordVerify from "@/components/PayPasswordVerify.vue";
// import { getBankCardList, unbindBankCard, verifyPayPasswordAPI } from '@/api/index.js'

// 响应式数据
const pagingRef = ref(null);
const bankCardList = ref([]);
const loading = ref(false);
const showPasswordVerify = ref(false);
const currentUnbindCard = ref(null);

// 银行数据配置 - 根据银行代码匹配背景图和图标
const bankConfig = reactive({
  // 交通银行
  COMM: {
    name: "交通银行",
    background: "/static/yh/jtyh.png",
    icon: "/static/yh/jtyhIcon.png",
  },
  // 中国工商银行
  ICBC: {
    name: "中国工商银行",
    background: "/static/yh/zggsyh.png",
    icon: "/static/yh/zggsyhIcon.png",
  },
  // 中国农业银行
  ABC: {
    name: "中国农业银行",
    background: "/static/yh/zgnyyh.png",
    icon: "/static/yh/zgnyyhIcon.png",
  },
  // 默认银行卡样式
  DEFAULT: {
    name: "银行卡",
    background: "/static/yh/jtyh.png", // 使用交通银行作为默认背景
    icon: "/static/yh/jtyhIcon.png", // 使用交通银行图标作为默认
  },
});

/**
 * 根据银行代码获取银行背景图
 * @param {string} bankCode - 银行代码
 * @returns {string} 背景图路径
 */
const getBankBackground = (bankCode) => {
  const config = bankConfig[bankCode] || bankConfig["DEFAULT"];
  return config.background;
};

/**
 * 根据银行代码获取银行图标
 * @param {string} bankCode - 银行代码
 * @returns {string} 图标路径
 */
const getBankIcon = (bankCode) => {
  const config = bankConfig[bankCode] || bankConfig["DEFAULT"];
  return config.icon;
};

/**
 * 根据银行代码获取银行名称
 * @param {string} bankCode - 银行代码
 * @returns {string} 银行名称
 */
const getBankName = (bankCode) => {
  const config = bankConfig[bankCode] || bankConfig["DEFAULT"];
  return config.name;
};

/**
 * 查询银行卡列表
 * @param {number} pageNo - 页码
 * @param {number} pageSize - 每页数量（银行卡列表一次性获取全部，此参数暂未使用）
 */
const queryBankCardList = async (pageNo, pageSize) => {
  try {
    loading.value = true;

    // 银行卡列表是一次性获取全部数据，只在第一页时请求
    if (pageNo === 1) {
      // 可以选择使用真实API或模拟数据
      // const result = await getBankCardList()

      // 当前使用模拟数据，实际开发时取消上面注释并删除下面的模拟数据
      const mockData = [
        {
          id: "1",
          bankCode: "COMM",
          cardNumber: "6222021234567890018",
          cardType: "储蓄卡",
          bindTime: "2024-01-15",
        },
        {
          id: "2",
          bankCode: "ICBC",
          cardNumber: "6222021234567890019",
          cardType: "储蓄卡",
          bindTime: "2024-02-20",
        },
        {
          id: "3",
          bankCode: "ABC",
          cardNumber: "6222021234567890020",
          cardType: "储蓄卡",
          bindTime: "2024-03-10",
        },
      ];

      // 模拟网络延迟
      await new Promise((resolve) => setTimeout(resolve, 1000));

      pagingRef.value?.complete(mockData);
    } else {
      // 非第一页直接返回空数组
      pagingRef.value?.complete([]);
    }
  } catch (error) {
    console.error("获取银行卡列表失败:", error);
    uni.showToast({
      title: "获取银行卡列表失败",
      icon: "none",
    });
    pagingRef.value?.complete(false);
  } finally {
    loading.value = false;
  }
};

/**
 * 下拉刷新处理
 */
const onRefresh = () => {
  queryBankCardList(1, 10);
};

/**
 * 银行卡点击处理
 * @param {object} item - 银行卡信息
 */
const handleCardClick = (item) => {
  console.log("点击银行卡:", item);
  // 可以在这里添加查看银行卡详情的逻辑
};

/**
 * 解绑银行卡
 * @param {object} item - 银行卡信息
 */
const handleUnbind = (item) => {
  uni.showModal({
    title: "确认解绑",
    content: `确定要解绑${getBankName(
      item.bankCode
    )}卡号尾号${item.cardNumber.slice(-4)}吗？`,
    success: (res) => {
      if (res.confirm) {
        // 保存当前要解绑的银行卡信息
        currentUnbindCard.value = item;
        // 显示支付密码验证弹窗
        showPasswordVerify.value = true;
      }
    },
  });
};

/**
 * 执行解绑银行卡操作（支付密码验证成功后调用）
 * @param {object} item - 银行卡信息
 */
const performUnbindBankCard = async (item) => {
  try {
    uni.showLoading({
      title: "解绑中...",
    });

    // 可以选择使用真实API或模拟数据
    // await unbindBankCard(item.id)

    // 当前使用模拟请求，实际开发时取消上面注释并删除下面的模拟延迟
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // 从列表中移除该银行卡
    const index = bankCardList.value.findIndex((card) => card.id === item.id);
    if (index > -1) {
      bankCardList.value.splice(index, 1);
    }

    uni.hideLoading();
    uni.showToast({
      title: "解绑成功",
      icon: "success",
    });
  } catch (error) {
    console.error("解绑银行卡失败:", error);
    uni.hideLoading();
    uni.showToast({
      title: "解绑失败，请重试",
      icon: "none",
    });
  }
};

/**
 * 支付密码验证函数
 * @param {string} password - 输入的支付密码
 * @returns {Promise<boolean>} 验证结果
 */
const verifyPayPassword = async (password) => {
  try {
    // 可以选择使用真实API或模拟验证
    // const result = await verifyPayPasswordAPI(password)
    // return result

    // 当前使用模拟验证，实际开发时取消上面注释并删除下面的模拟逻辑
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 模拟验证逻辑（实际开发中应该调用后端接口）
    // 这里设置测试密码为 123456，您可以根据需要修改
    return password === "123456";
  } catch (error) {
    console.error("支付密码验证失败:", error);
    return false;
  }
};

/**
 * 支付密码验证成功回调
 * @param {string} password - 验证成功的密码
 */
const onPasswordVerifySuccess = (password) => {
  console.log("支付密码验证成功:", password);

  // 执行银行卡解绑操作
  if (currentUnbindCard.value) {
    performUnbindBankCard(currentUnbindCard.value);
    currentUnbindCard.value = null;
  }
};

/**
 * 支付密码验证失败回调
 * @param {string} errorMessage - 错误信息
 */
const onPasswordVerifyFailed = (errorMessage) => {
  console.log("支付密码验证失败:", errorMessage);
  uni.showToast({
    title: errorMessage,
    icon: "none",
  });
};

/**
 * 支付密码验证取消回调
 */
const onPasswordVerifyCancel = () => {
  console.log("用户取消支付密码验证");
  currentUnbindCard.value = null;
};

/**
 * 设置支付密码
 */
const handleSetPayPassword = () => {
  // 跳转到设置支付密码页面
  uni.navigateTo({
    url: "/pages/my/payPasswordSetting",
  });
};

/**
 * 添加银行卡
 */
const handleAddBank = () => {
  // 跳转到添加银行卡页面
  uni.navigateTo({
    url: "/pages/my/addBankCard",
  });
};

// 页面挂载时的初始化
onMounted(() => {
  console.log("银行卡列表页面已挂载");
});

// 全部返回卡列表页面
onBackPress(() => {
  uni.navigateTo({
    url: "/pages/my/homePage",
  });
  return true;
});
</script>

<style lang="scss" scoped>
.bank-list-page {
  height: 100vh;
  background-color: #f7f7f7;
}

.bank-card-list {
  padding: 32rpx 32rpx 0;
}

.bank-card-item {
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

/* 银行卡片样式 */
.bank-card {
  height: 224rpx;
  border-radius: 16rpx;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);

  /* 添加渐变遮罩以确保文字可读性 */
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.1) 0%,
      rgba(0, 0, 0, 0.3) 100%
    );
    z-index: 1;
  }

  /* 确保内容在遮罩之上 */
  .bank-info,
  .card-info {
    position: relative;
    z-index: 2;
  }
}

/* 银行信息区域 */
.bank-info {
  .bank-header {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .bank-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 16rpx;
      border-radius: 8rpx;
      background-color: rgba(255, 255, 255, 0.2);
      padding: 8rpx;
    }

    .bank-name {
      color: #ffffff;
      font-size: 32rpx;
      font-weight: 600;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
    }
  }

  .card-type {
    color: rgba(255, 255, 255, 0.9);
    font-size: 24rpx;
    margin-left: 64rpx;
  }
}

/* 卡号信息区域 */
.card-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;

  .card-number {
    display: flex;
    align-items: baseline;

    .card-dots {
      color: rgba(255, 255, 255, 0.8);
      font-size: 28rpx;
      margin-right: 16rpx;
      letter-spacing: 4rpx;
    }

    .card-last-four {
      color: #ffffff;
      font-size: 36rpx;
      font-weight: 600;
      font-family: "Courier New", monospace;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
    }
  }

  .card-actions {
    .unbind-btn {
      background-color: rgba(255, 255, 255, 0.2);
      color: #ffffff;
      font-size: 24rpx;
      padding: 12rpx 24rpx;
      border-radius: 20rpx;
      border: 1rpx solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(10rpx);
      transition: all 0.3s ease;

      &:active {
        background-color: rgba(255, 255, 255, 0.3);
        transform: scale(0.95);
      }
    }
  }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;

  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 32rpx;
    opacity: 0.6;
  }

  .empty-text {
    color: #999999;
    font-size: 32rpx;
    margin-bottom: 16rpx;
  }

  .empty-tip {
    color: #cccccc;
    font-size: 28rpx;
  }
}

/* 底部固定区域样式 */
.bottom-section {
  padding: 32rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;

  .button-row {
    display: flex;
    gap: 24rpx;

    .action-btn {
      flex: 1;
      height: 88rpx;
      font-size: 32rpx;
      font-weight: 600;
      border-radius: 44rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &.primary {
        background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
        color: #ffffff;
        box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.3);

        &:active {
          transform: scale(0.98);
          box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.4);
        }
      }

      &.secondary {
        background-color: #f8f9fa;
        color: #333333;
        border: 2rpx solid #e9ecef;

        &:active {
          background-color: #e9ecef;
          transform: scale(0.98);
        }
      }
    }
  }
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .bank-card {
    height: 200rpx;
    padding: 24rpx;

    .bank-info .bank-header {
      .bank-icon {
        width: 40rpx;
        height: 40rpx;
      }

      .bank-name {
        font-size: 28rpx;
      }
    }

    .card-info .card-number {
      .card-dots {
        font-size: 24rpx;
      }

      .card-last-four {
        font-size: 32rpx;
      }
    }
  }
}
</style>
