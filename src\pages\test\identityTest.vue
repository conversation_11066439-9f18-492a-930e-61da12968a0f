<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">身份验证功能测试</text>
    </view>
    
    <view class="test-content">
      <button class="test-btn" @click="goToIdentityPage">
        跳转到身份验证页面
      </button>
      
      <view class="test-info">
        <text class="info-title">测试说明：</text>
        <text class="info-text">1. 点击按钮跳转到身份验证页面</text>
        <text class="info-text">2. 输入有效的手机号（11位，以1开头）</text>
        <text class="info-text">3. 点击"获取验证码"按钮</text>
        <text class="info-text">4. 输入6位验证码</text>
        <text class="info-text">5. 点击"下一步"进行验证</text>
      </view>
      
      <view class="api-info">
        <text class="info-title">API接口：</text>
        <text class="info-text">发送验证码：/app/enrollment/sms/otc/send</text>
        <text class="info-text">验证验证码：/app/enrollment/sms/otc/verify</text>
      </view>
    </view>
  </view>
</template>

<script setup>
// 跳转到身份验证页面
const goToIdentityPage = () => {
  uni.navigateTo({
    url: '/pages/my/identity'
  })
}
</script>

<style lang="scss" scoped>
.test-container {
  padding: 40rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.test-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.test-content {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
}

.test-btn {
  width: 100%;
  height: 88rpx;
  background-color: #00b781;
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 40rpx;
}

.test-info,
.api-info {
  margin-bottom: 40rpx;
}

.info-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.info-text {
  display: block;
  font-size: 24rpx;
  color: #666666;
  line-height: 40rpx;
  margin-bottom: 10rpx;
}
</style>
