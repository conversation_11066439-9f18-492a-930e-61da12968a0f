


.btn-primary{
    color:#ffffff !important;
    background-color:$uni-color-primary !important;
    border-color:$uni-color-primary !important;
    font-size:16px !important;
    height: 46px;
    line-height: 46px;
}

.btn-primary[disabled='true']{
    background-color: #DFDFDF!important;
}

.btn-primary[aria-disabled='true']{
    background-color: #DFDFDF!important;
}



.btn-info{
    color:#979797 !important;
    background-color:#ffffff !important;
    font-size:14px !important;
}

.btn-plain{
    border: 1px solid #B3B3B3 !important;
}

.btn-status-1 {
    background-color: #ffc328 !important;
    font-size: 12px !important;
}
.btn-status-2 {
    background-color: #f72828 !important;
    font-size: 12px !important;
}
.btn-status-3 {
}

.primary{
    color: $uni-color-primary;
}
.grey{
    color: #F5F5F7;
}

.text-center{
    text-align: center;
}

.flex{
    display: flex;
}

.flex-center{
    display: flex;
    align-items: center;
    justify-content: center;
}
.flex-space-between{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.flex-space-evenly{
    display: flex;
    align-items: center;
    justify-content: space-evenly;
}

.active{
    color:$uni-color-primary !important;
}


.danger{
    color: #FF3A30;
  }


.grey-tips{
    color:#C5C5C5;
    font-size: 10px;
}

.split-line{    
    height:8px;
    width: 100%;
    background: #F6F6F6;
}

.ellipsis{    
    overflow: hidden;    
    text-overflow: ellipsis;    
    white-space: nowrap;
}

.no-scrollbar {
    scrollbar-width: none;
    scrollbar-color: transparent transparent;
}

@media (min-width: 520px) {
    body{
        background: #f8f7fb;
    }
    uni-page {
		width: 414px !important; 
		overflow-y: auto;
		min-width: 300px !important;
        margin: 0 auto;
        box-shadow: #999 0 0 12px;        
    
	}
	uni-page-body {
		width: 414px !important; 
	}    
}