<script setup>
import { ref, onMounted } from "vue";
import { onLaunch } from "@dcloudio/uni-app";
import { useSafeStorage } from "@/hooks/useSafeStorage";

// #ifdef H5
import VConsole from "vconsole";
// #endif

// 生命周期钩子
onMounted(() => {
  // #ifdef H5
  document.addEventListener(
    "touchstart",
    (event) => {
      if (event.touches.length > 1) {
        event.preventDefault();
      }
    },
    { passive: false }
  );

  document.addEventListener("gesturestart", (event) => {
    event.preventDefault();
  });
  // #endif
});

// 当uni-app 初始化完成时触发（全局只触发一次），参数为应用启动参数
onLaunch(() => {
  // 添加全局路由拦截器
  uni.addInterceptor("navigateTo", {
    // 拦截 uni.navigateTo
    invoke(args) {
      console.log(args, "路由跳转前触发");

      // 路由跳转前触发
      const needAuthPages = [
        "/pages/my/setting", // 需要鉴权的页面路径
      ];

      // 检查是否需要鉴权
      if (needAuthPages.includes(args.url)) {
        const loginInfo = useSafeStorage("checkIn-loginInfo") || {}; // 检查 localStorage
        console.log(loginInfo, "全局路由拦截器");
        if (!loginInfo.value.reportId) {
          // 没有权限，跳转到首页
          uni.reLaunch({ url: "/pages/home/<USER>" });
          return false; // 阻止原始跳转
        }
      }

      // 放行跳转
      return args;
    },
    fail(err) {
      console.error("路由跳转失败:", err);
    },
  });
});
</script>

<style lang="scss">
/* 样式部分保持不变 */
page {
  font-family: PingFangSC, PingFang SC;
  font-size: 28rpx;
  color: #4d4d4d;
}

:deep(.uni-nav-bar-text) {
  font-size: 16px !important;
  font-weight: 600 !important;
}
</style>
