<template>
    <view class="city-container">
        <view class="city-body">
            <view class="city-search">
                <view class="input-warp">
                    <uni-icons type="search" size="20"></uni-icons>
                    <input @input="search" v-model="state.searchVal" class="input" :placeholder="t('search')" />
                </view>

                <!-- <view class=city-current style="padding-left: 26px;" v-if="state.current.regionName">
                    <text class="text">{{ t('currentCity') }}</text> 
                    <view class="btn-card">{{ state.current.regionName || t('pleaseSelect') }}</view>
                </view> -->
            </view>
            <view class="city-content-warp">
                <scroll-view class="city-content" scroll-y="true" @scroll="handleScroll"
                    :scroll-into-view="state.scrollIntoViewId" scroll-with-animation>
                    <view class="city-hot" v-if="hotRegion.length">
                        <text class="text">{{ t('hotCities') }}</text>
                        <view class="hot-list">
                            <view class="hot-item-warp" v-for="(i, j) in hotRegion" :key="j" @click="setCurrent(i)">
                                <view class="hot-item">
                                    <view class="btn-card" :class="{ 'btn-active': i.id == state.current.id }">
                                        {{ i.hot_regionName }}</view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view class="city-group-warp">
                        <view class="city-group"
                            v-for="(value, key) of Object.keys(state.searchList).length ? state.searchList : regionTree"
                            :key="key" :id="key">
                            <view class="group-title">{{ key }}</view>
                            <view class="group-list">
                                <view class="group-item" :class="{ 'active': i.id == state.current.id }"
                                    v-for="(i, j) in value" :key="j" @click="setCurrent(i)">
                                    {{ i.regionName }}
                                </view>
                            </view>
                        </view>
                    </view>
                </scroll-view>
            </view>
            <view class="city-sort">
                <view class="sort-item" v-for="(i, j) in letters" :key="j" @click="toName(i)"
                    :class="{ 'active': i == state.activeId }">
                    {{ i }}
                </view>
            </view>
        </view>
        <view class="city-footer">
            <slot name="footer"></slot>
        </view>
    </view>
</template>

<script setup>

import { nextTick, reactive, onMounted } from 'vue';
import useStore from "@/store"
import { debounce, throttle } from "@/utils"
import { t } from '@/lang';

const emit = defineEmits(['change'])
const { user } = useStore()

const regionTree = computed(() => {
    return user.regionTree
})

const hotRegion = computed(() => user.hotRegion)

const state = reactive({
    current: {},
    scrollIntoViewId: null,
    searchList: {},
    activeId: null,

    scrollViewHeight: 0,
    itemPositions: [],
    currentScrollTop: 0,
})

const letters = computed(() => {
    if (Object.keys(state.searchList).length) {
        return Object.keys(state.searchList)
    }
    return Object.keys(user.regionTree)
})

function setCurrent(item) {
    state.current = { ...item }
    emit('change', item)
}

function toName(id) {
    state.scrollIntoViewId = id
    state.activeId = id;
    nextTick(() => {
        state.scrollIntoViewId = ''
    })
}

const handleScroll = throttle(function (e) {
    const scrollTop = e.detail.scrollTop;
    state.currentScrollTop = scrollTop;

    manualCheckActiveItem()
    
}, 200)

let manualCheckTimer = null
function manualCheckActiveItem() {
    // 清除之前的定时器
    if (manualCheckTimer) clearTimeout(manualCheckTimer)
    
    // 延迟执行，避免频繁计算
    manualCheckTimer = setTimeout(() => {
        const visibleCenter = state.currentScrollTop + state.scrollViewHeight/2
        
        // 找出哪个元素的中心最接近可视区域中心
        let closestItem = null
        let minDistance = Infinity
        
        state.itemPositions.forEach(pos => {
        const itemCenter = pos.top + pos.height/2
        const distance = Math.abs(itemCenter - visibleCenter)
        
        if (distance < minDistance) {
            minDistance = distance
            closestItem = pos.id
        }
        })
        
        if (closestItem && state.activeId !== closestItem) {
            state.activeId = closestItem
        }
    }, 100)
}

let observer = null
function initObserver() {
  observer = uni.createIntersectionObserver(this)
  observer.relativeTo('.city-content').observe('.group-title', res => {
    if (res.intersectionRatio > 0) {
      // 当元素进入视口时，启动手动检查
      manualCheckActiveItem()
    }
  })
}

function calculatePositions() {
    const query = uni.createSelectorQuery().in(this);


    query.select('.city-content').boundingClientRect(res => {
        state.scrollViewHeight = res.height
    }).exec()

    Object.keys(regionTree.value).forEach(item => {
        const dom = document.getElementById(`${item}`)
        const { top, height } = dom.getBoundingClientRect()

        state.itemPositions.push({ id: item, top, height });
    });

    initObserver()

    nextTick(() => {
        console.log(state.itemPositions, state.scrollViewHeight, Object.keys(regionTree.value))

        initObserver()
    })
}

onMounted(() => {

    // 计算每个内容项的位置
    calculatePositions();
})




const search = debounce(function (e) {
    const val = e.detail.value
    const arr = user.allRegionTree.filter(i => i.regionName.indexOf(val) != -1)
    const letters = Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i));
    const groupedCities = letters.reduce((acc, letter) => {
        const matchedCities = arr.filter(city =>
            city.regionSort === letter
        );
        if (matchedCities.length > 0) {
            acc[letter] = matchedCities;
        }
        return acc;
    }, {});
    state.searchList = groupedCities
}, 300)



</script>

<style lang="scss" scoped>
.city-container {
    position: relative;
    height: 80vh;
    padding-bottom: 50px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .city-body {
        flex: 1;
        display: flex;
        flex-direction: column;
        position: relative;

        .city-search {}

        .city-content-warp {
            padding: 0 26px;
        }

        .city-content {
            // flex:1;
            width: 100%;
            // overflow-y: auto;

            // padding: 0 26px;
            height: calc(80vh - 190px);
        }

        .city-sort {
            position: absolute;
            right: 0;
            top: 60px;
            bottom: 0;
            z-index: 20;
            background-color: #F5F5F7;

            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .sort-item {
                text-align: center;
                font-size: 14px;
                color: #AAAAAA;
                height: 25px;
                line-height: 25px;
                width: 16px;
                padding: 0 5px;

                &:active {
                    color: #000;
                }
            }
        }
    }

    .city-footer {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
    }

    .input-warp {
        display: flex;
        align-items: center;
        background: #E6E6E6;
        border-radius: 8px 8px 8px 8px;
        height: 36px;
        line-height: 36px;
        margin: 10px 35px;
        padding: 0 14px;

        .input {
            flex: 1;
            height: 36px;
            line-height: 36px;
            padding-left: 6px;
            font-size: 14px;
        }
    }

    .city-current {
        display: flex;
        align-items: center;
        padding: 15px 0;
        border-top: 0.5px solid #AAAAAA;

        .text {
            margin-right: 10px;
        }
    }

    .btn-card {
        padding: 9px 23px;
        font-size: 14px;
        color: #000000;
        background: #E6E6E6;
        border-radius: 8px 8px 8px 8px;
        cursor: pointer;
        text-align: center;
    }

    .btn-card.btn-active {
        background-color: $uni-color-primary;
        color: #fff;

    }

    .city-hot {
        padding: 15px 0;
        border-top: 0.5px solid #AAAAAA;
        // margin-bottom: 15px;

        .hot-list {
            margin-top: 10px;
            display: flex;
            flex-wrap: wrap;

            .hot-item-warp {
                // width: 25%;
                // &:nth-child(4n) {
                //     .hot-item {
                //         margin-right: 0;
                //     }
                // }
            }

            .hot-item {
                margin-right: 20px;
                margin-bottom: 14px;
            }
        }
    }

    .city-group {

        .group-title {
            padding: 14px 0 2px 9px;
            font-size: 14px;
            color: #AAAAAA;
        }

        .group-list {}

        .group-item {
            border-top: 0.5px solid #AAAAAA;
            padding: 10px 0 10px 9px;

            &:first-child {
                border-top: 0;
            }
        }

        .group-item.active {
            color: #fff !important;
            background-color: $uni-color-primary;
        }
    }


}
</style>