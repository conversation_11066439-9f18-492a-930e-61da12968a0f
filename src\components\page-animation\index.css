/* #ifdef H5 */
uni-page {
	/* opacity: 0; */
}

uni-page.animation-before {
	/* 在页面上使用 transform 会导致页面内的 fixed 定位渲染为 absolute，需要在动画完成后移除 */
	/* transform: translateY(20px); */	
	transform: translateX(-100%);
}

uni-page.animation-leave {
	transition: all .3s ease;		
}

uni-page.animation-enter {
	transition: all .3s ease;
	transform: translateX(100%);	
}

uni-page.animation-show {
	opacity: 1;	
}

uni-page.animation-after {
	/* 在页面上使用 transform 会导致页面内的 fixed 定位渲染为 absolute，需要在动画完成后移除 */
	/* transform: translateX(0);*/
	transform: translateY(0);	
}


.slide_left-enter-active,
.slide_left-leave-active,
.slide_right-enter-active,
.slide_right-leave-active {
    transition: all 0.25s;
    position: absolute !important;
    background-color: white;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 1;
}

.slide_left-enter-from,
.slide_right-leave-to {
    opacity: 1;
    transform: translateX(100%);
}

.slide_right-enter-from,
.slide_left-leave-to {
    opacity: 1;
    transform: translateX(-100%);
}

.slide_left-leave-to,
.slide_right-leave-to {
    opacity: 0.25;
}

/* #endif */
