<template>
  <view class="test-page">
    <view class="test-header">
      <text class="test-title">支付密码验证组件测试</text>
    </view>
    
    <view class="test-content">
      <view class="test-info">
        <text class="info-text">测试密码：123456</text>
      </view>
      
      <view class="test-buttons">
        <button class="test-btn" @click="showPasswordVerify = true">
          测试支付密码验证
        </button>
      </view>
      
      <view class="test-result" v-if="lastResult">
        <text class="result-title">最后验证结果：</text>
        <text class="result-text" :class="lastResult.success ? 'success' : 'failed'">
          {{ lastResult.message }}
        </text>
      </view>
    </view>
    
    <!-- 支付密码验证组件 -->
    <PayPasswordVerify 
      v-model:visible="showPasswordVerify"
      :verify-function="testVerifyFunction"
      @success="onVerifySuccess"
      @failed="onVerifyFailed"
      @cancel="onVerifyCancel"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import PayPasswordVerify from '@/components/PayPasswordVerify.vue'

// 响应式数据
const showPasswordVerify = ref(false)
const lastResult = ref(null)

/**
 * 测试用的支付密码验证函数
 * @param {string} password - 输入的密码
 * @returns {Promise<boolean>} 验证结果
 */
const testVerifyFunction = async (password) => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1500))
  
  // 测试密码为 123456
  return password === '123456'
}

/**
 * 验证成功回调
 */
const onVerifySuccess = (password) => {
  lastResult.value = {
    success: true,
    message: `验证成功！密码：${password}`
  }
  console.log('验证成功:', password)
}

/**
 * 验证失败回调
 */
const onVerifyFailed = (errorMessage) => {
  lastResult.value = {
    success: false,
    message: `验证失败：${errorMessage}`
  }
  console.log('验证失败:', errorMessage)
}

/**
 * 取消验证回调
 */
const onVerifyCancel = () => {
  lastResult.value = {
    success: false,
    message: '用户取消了验证'
  }
  console.log('用户取消验证')
}
</script>

<style lang="scss" scoped>
.test-page {
  padding: 32rpx;
  background-color: #f7f7f7;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .test-title {
    font-size: 40rpx;
    font-weight: 600;
    color: #333333;
  }
}

.test-content {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  
  .test-info {
    margin-bottom: 40rpx;
    padding: 24rpx;
    background-color: #f0f8ff;
    border-radius: 12rpx;
    
    .info-text {
      font-size: 28rpx;
      color: #007AFF;
    }
  }
  
  .test-buttons {
    margin-bottom: 40rpx;
    
    .test-btn {
      width: 100%;
      height: 88rpx;
      background-color: #007AFF;
      color: #ffffff;
      font-size: 32rpx;
      border-radius: 44rpx;
      border: none;
      
      &:active {
        background-color: #0056b3;
      }
    }
  }
  
  .test-result {
    padding: 24rpx;
    background-color: #f8f9fa;
    border-radius: 12rpx;
    
    .result-title {
      font-size: 28rpx;
      color: #666666;
      display: block;
      margin-bottom: 12rpx;
    }
    
    .result-text {
      font-size: 30rpx;
      font-weight: 500;
      
      &.success {
        color: #28a745;
      }
      
      &.failed {
        color: #dc3545;
      }
    }
  }
}
</style>
