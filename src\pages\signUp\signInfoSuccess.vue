<template>
  <view class="signInfoSuccess">
    <div class="successBox">
      <view>
        <image src="@/static/image/success.png" class="successImg"></image>
      </view>

      <view class="title"> 恭喜你：{{ personalInfo.name || "" }}</view>
      <view class="content">
        你已成功报名{{ schoolName || "" }}{{ planName || ""
        }}{{ selectSpecialty.majorName || "" }}专业</view
      >
    </div>

    <view class="bottom-fixed">
      <button class="submit-btn" @click="handleSubmit">完成</button>
    </view>
  </view>
</template>

<script setup>
import useSignUpStore from "@/store/signUp";
import { useSafeStorage } from "@/hooks/useSafeStorage";

const signUpStore = useSignUpStore();

const personalInfo = computed(() => signUpStore.getPersonalInfo);
const selectSpecialty = useSafeStorage("selectSpecialty", {});

const schoolName = uni.getStorageSync("schoolName") || "";
const planName = uni.getStorageSync("planName") || "";

const handleSubmit = () => {
  signUpStore.resetPersonalInfo(); // 点击完成清楚数据
  uni.navigateTo({
    url: "/pages/home/<USER>",
  });
};
</script>

<style scoped>
.signInfoSuccess {
  background: #f6f6f6;
  height: 100vh;
  position: relative;
}
.successBox {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 50%;
  padding-left: 18rpx;
  padding-right: 18rpx;
}
.successImg {
  width: 80px;
  height: 80px;
}

.title {
  font-weight: 600;
  font-size: 34rpx;
  color: #333333;
}

.content {
  font-weight: 400;
  font-size: 28rpx;
  color: #8c8c8c;
}

.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
}

.submit-btn {
  width: 100%;
  height: 80rpx;
  background: #00b781;
  color: #ffffff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
</style>
