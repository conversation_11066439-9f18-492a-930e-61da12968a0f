{"name": "uni-demo", "version": "1.0.0", "description": "", "scripts": {"dev:h5": "uni --mode uat", "dev:mp-weixin": "uni -p mp-weixin", "dev:docs": "vitepress dev docs --port 8000", "build:docs": "vitepress build docs", "preview:docs": "vitepress preview docs", "dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:app-harmony": "uni -p app-harmony", "dev:custom": "uni -p", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:app-harmony": "uni build -p app-harmony", "build:custom": "uni build -p", "build:h5": "uni build --mode uat", "build:uatrelease": "uni build --mode uatrelease", "build:production": "uni build --mode production", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "lint": "eslint src", "lint:fix": "eslint src --fix", "lint:css": "stylelint --fix \"src/**/*.{css,scss,vue}\""}, "uni-app": {"scripts": {"mp-dingtalk": {"title": "微信公众号", "env": {"UNI_PLATFORM": "mp-alipay"}, "define": {"MP-DINGTALK": true}}}}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@dcloudio/uni-app": "3.0.0-4020420240722003", "@dcloudio/uni-app-harmony": "3.0.0-4020420240722003", "@dcloudio/uni-app-plus": "3.0.0-4020420240722003", "@dcloudio/uni-components": "3.0.0-4020420240722003", "@dcloudio/uni-h5": "3.0.0-4020420240722003", "@dcloudio/uni-mp-alipay": "3.0.0-4020420240722003", "@dcloudio/uni-mp-weixin": "3.0.0-4020420240722003", "@dcloudio/uni-quickapp-webview": "3.0.0-4020420240722003", "@dcloudio/uni-ui": "^1.5.6", "cropperjs": "1.6.1", "dayjs": "^1.11.9", "encryptlong": "^3.1.4", "exif-js": "^2.3.0", "html2canvas": "^1.4.1", "html5-qrcode": "^2.3.8", "pinia": "^2.2.4", "pinia-plugin-persistedstate": "^4.1.2", "vconsole": "^3.15.1", "vue": "^3.4.21", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4020420240722003", "@dcloudio/uni-cli-shared": "3.0.0-4020420240722003", "@dcloudio/uni-stacktracey": "3.0.0-4020420240722003", "@dcloudio/vite-plugin-uni": "3.0.0-4020420240722003", "@eslint/js": "^9.11.1", "@vue/eslint-config-standard": "^8.0.1", "@vue/runtime-core": "^3.4.21", "deep-pick-omit": "^1.2.1", "destr": "^2.0.3", "eslint": "^9.11.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.28.0", "globals": "^15.9.0", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "sass": "1.25.0", "sass-loader": "8.0.1", "stats.js": "^0.17.0", "stylelint": "^16.9.0", "stylelint-config-html": "^1.1.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-scss": "1.0.0-security", "stylelint-config-standard": "^36.0.1", "stylelint-order": "^6.0.4", "stylelint-prettier": "^5.0.2", "stylelint-scss": "^6.7.0", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^28.4.1", "vite": "5.2.8", "vite-plugin-html": "^3.2.2", "vitepress": "^1.3.4"}}