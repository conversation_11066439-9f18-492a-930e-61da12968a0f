<template>
    <view class="formPage">
        <uni-nav-bar left-icon="left" statusBar fixed :title="navTitle" @clickLeft="back" :border="false"></uni-nav-bar>
        <view class="form-container">
            <view class="split-line" v-if="!hideSplitLine"></view>
            <view class="form-content" :class="[contentClass]">
                <view class="form-title">
                    <slot name="title">
                        <view class="form-title-text" v-if="title">
                            {{title}}
                        </view>
                    </slot>
                </view>
                <view class="form-body">
                    <!--  -->
                    <slot></slot>
                    <!--  -->
                </view>
            </view>
        </view>
        <view class="form-footer">
            <slot name="submit">
                <button :disabled="submitDisabled" type="primary" class="btn-primary" :loading="submitLoading" @click="submit">{{submitText}}</button>
            </slot>
            <slot name="tips">
                <view class="form-tips" v-if="tips" @click="tipsClick">{{tips}}</view>
            </slot>
        </view>
    </view>
</template>

<script setup>

const emit = defineEmits(['submit','tipsClick'])

const props = defineProps({
    navTitle: {
        type: String,
        default: ""
    },
    title: {
        type: String,
        default: ""
    },
    hideSplitLine: {
        type: Boolean,
        default: false
    },
    tips:{
        type:String,
        default:""
    },
    submitText :{
        type:String,
        default:"保存"
    },
    submitLoading: {
        type: Boolean,
        default: false 
    },
    submitDisabled:{
        type: Boolean,
        default: false
    },
    contentClass:{
        type:String,
        default:""
    }    
})

const back = () => {
    uni.navigateBack({
        delta: 1
    })
}

const tipsClick = ()=>{
    emit('tipsClick')
}


const submit = ()=>{
    emit('submit')
}

</script>

<style lang="scss" scoped>
.formPage {
    min-height: 100vh;
    position: relative;
    background-color:#F6F6F6; 
    .form-content {
        background-color:#fff; 
        padding: 15px 0;
        .form-title-text{
            padding: 41px 30px;
            text-align: center;
            font-weight: 600;
            font-size: 18px;
            color: #2F2F2F;
        }
    }
    .form-footer {
        position: absolute;
        bottom: 25px;
        left: 16px;
        right: 16px;
        padding-bottom: env(safe-area-inset-bottom);
        .form-tips{
            padding: 12px 0;
            text-align: center;
            color:#969696;   
        }        
    }
}
</style>