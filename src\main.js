import {  createSSRApp } from 'vue'
import App from './App.vue'
import * as <PERSON><PERSON> from 'pinia';
import { createPersistedState } from 'pinia-plugin-persistedstate';
import "@/styles/index.scss"


export function createApp() {
    const app = createSSRApp(App)
    const pinia = Pinia.createPinia()

    pinia.use(createPersistedState({
        storage: {
            getItem: (key) => {
                return uni.getStorageSync(key)
            },
            setItem: (key, value) => {
                uni.setStorageSync(key, value)
            }
        }
    }))

    app.use(pinia);
    return {
        app,
        Pinia,
    }
}