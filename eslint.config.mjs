import globals from "globals";
import pluginJs from "@eslint/js";
import pluginVue from "eslint-plugin-vue";


export default [
  { 
    files: ["**/*.{js,mjs,cjs,vue}"],
  },
  {
    languageOptions: { globals: globals.browser },    
  },
  pluginJs.configs.recommended,
  ...pluginVue.configs["flat/essential"],
  {
    rules: {      
      'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off', 
      'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off', 
      'vue/multi-word-component-names': 'off', 
      'vue/script-indent': ['error', 4, { baseIndent: 1 }], // 设置 Vue 脚本部分的缩进为 4 个空格
      'vue/html-indent': ['error', 4], // 设置 Vue 模板部分的缩进为 4 个空格           
      "no-restricted-globals": ["error", "process"]
    },
    "globals":{
      "process": "readonly"
    }
  },
  
];