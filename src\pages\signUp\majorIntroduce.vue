<template>
  <view class="page-container">
    <!-- 滚动内容区域 -->
    <scroll-view
      scroll-y="true"
      class="content-scroll"
      :show-scrollbar="false"
      enhanced
    >
      <view class="content-container">
        <view class="radio_box_con" @click="selectMajor">
          <view
            :class="{
              active: copyMajorIntroduce.selected,
              radio_box: true,
            }"
            ><view>选择</view></view
          >
        </view>

        <!-- 专业名称 -->
        <view class="info-section">
          <view class="section-title">专业名称</view>
          <view class="section-content">
            <view class="content-item">
              <view class="dot"></view>
              <text class="content-text">{{ majorIntroduce.majorName }}</text>
            </view>
          </view>
        </view>

        <!-- 学制 -->
        <view class="info-section">
          <view class="section-title">学制</view>
          <view class="section-content">
            <view class="content-item">
              <view class="dot"></view>
              <text class="content-text">{{
                majorIntroduce.educationalSystem
              }}</text>
            </view>
          </view>
        </view>

        <!-- 专业介绍 -->
        <view class="info-section">
          <view class="section-title">专业介绍</view>
          <view class="section-content">
            <view class="content-item">
              <view class="dot"></view>
              <text class="content-text long-text">{{
                majorIntroduce.majorIntroduction
              }}</text>
            </view>
          </view>
        </view>

        <!-- 核心课程 -->
        <view class="info-section">
          <view class="section-title">核心课程</view>
          <view class="section-content">
            <view class="content-item">
              <view class="dot"></view>
              <text class="content-text">{{
                majorIntroduce.coreCurriculum
              }}</text>
            </view>
          </view>
        </view>

        <!-- 就业方向 -->
        <view class="info-section">
          <view class="section-title">就业方向</view>
          <view class="section-content">
            <view class="content-item">
              <view class="dot"></view>
              <text class="content-text long-text">{{
                majorIntroduce.employmentDirection
              }}</text>
            </view>
          </view>
        </view>
        <!-- 学费 -->
        <view class="info-section">
          <view class="section-title">学费（元/年）</view>
          <view class="section-content">
            <view class="content-item">
              <view class="dot"></view>
              <text class="content-text">{{ majorIntroduce.amount }}元/年</text>
            </view>
          </view>
        </view>
        <!-- 底部留白 -->
        <view class="bottom-space"></view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from "vue";

import { useSafeStorage } from "@/hooks/useSafeStorage";

const majorIntroduce = useSafeStorage("majorIntroduce", {});

let copyMajorIntroduce = ref({
  selected: false,
});

copyMajorIntroduce.value = JSON.parse(JSON.stringify(majorIntroduce.value));

// 响应式数据
const statusBarHeight = ref(0);

// majorIntroduce 是个计算属性不能直接修改值
const selectMajor = () => {
  uni.vibrateShort(); // 短震动
  console.log(copyMajorIntroduce.value.selected, "copyMajorIntroduce");

  copyMajorIntroduce.value.selected = !copyMajorIntroduce.value.selected;

  uni.setStorageSync(
    "majorIntroduce",
    JSON.stringify(copyMajorIntroduce.value)
  );
  uni.showToast({
    title: `已${copyMajorIntroduce.value.selected ? "选中" : "取消选中"}：${
      copyMajorIntroduce.value.majorName
    }`,
    icon: "none",
    duration: 1500,
  });
};

// 生命周期
onMounted(() => {
  copyMajorIntroduce.value = JSON.parse(JSON.stringify(majorIntroduce.value));
});

onReady(() => {
  uni.setNavigationBarTitle({
    title: `${majorIntroduce.value.majorName}`,
  });
});
</script>

<style scoped>
.page-container {
  min-height: 100vh;
  background-color: #f6f6f6;
  display: flex;
  flex-direction: column;
}

/* 滚动内容区域 */
.content-scroll {
  flex: 1;
}

.content-container {
  padding: 40rpx 30rpx;
}

/* 信息区块 */
.info-section {
  margin-bottom: 48rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
  line-height: 44rpx;
}

.section-content {
  background-color: transparent;
}

.content-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  background-color: #00c853;
  border-radius: 50%;
  margin-top: 14rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.content-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
  flex: 1;
}

.long-text {
  text-align: justify;
  word-break: break-all;
}

.bottom-space {
  height: 60rpx;
}

/* 响应式适配 */
/* @media (max-width: 480px) {
  .content-container {
    padding: 30rpx 24rpx;
  }

  .section-title {
    font-size: 30rpx;
  }

  .content-text {
    font-size: 26rpx;
    line-height: 38rpx;
  }
} */

/* 滚动优化 */
.content-scroll {
  scroll-behavior: smooth;
}

/* 长文本优化 */
.long-text {
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
}

.radio_box_con {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.radio_box {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 180rpx;
  height: 50rpx;
  color: #333333;
  background-color: #f6f6f6;
  border: 2rpx solid #ccc;
}

.active {
  color: #00b781 !important;
  background-color: #f6f6f6;
  border: 2rpx solid #00b781;
}
</style>
