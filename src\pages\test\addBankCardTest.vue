<template>
  <view class="test-page">
    <view class="test-header">
      <text class="test-title">添加银行卡流程测试</text>
    </view>
    
    <view class="test-content">
      <view class="test-info">
        <view class="info-item">
          <text class="info-label">测试场景：</text>
          <text class="info-text">首次绑定银行卡流程</text>
        </view>
        <view class="info-item">
          <text class="info-label">流程说明：</text>
          <text class="info-text">1. 输入基本信息（姓名、手机号）</text>
          <text class="info-text">2. 点击设置支付密码 → 跳转到密码设置页面</text>
          <text class="info-text">3. 密码设置成功后返回，显示6个*号</text>
          <text class="info-text">4. 点击下一步，填写银行卡详细信息</text>
          <text class="info-text">5. 勾选协议并提交</text>
        </view>
        <view class="info-item">
          <text class="info-label">注意事项：</text>
          <text class="info-text">• 所有表单项都是必填的</text>
          <text class="info-text">• 必须设置支付密码才能进入下一步</text>
          <text class="info-text">• 必须勾选协议才能提交</text>
        </view>
      </view>
      
      <view class="test-buttons">
        <button class="test-btn primary" @click="testFirstBinding">
          测试首次绑定银行卡
        </button>
        
        <button class="test-btn secondary" @click="testExistingUserBinding">
          测试已有用户绑定银行卡
        </button>
      </view>
      
      <view v-if="lastResult" class="test-result">
        <text class="result-title">最后操作结果：</text>
        <text class="result-text" :class="lastResult.success ? 'success' : 'failed'">
          {{ lastResult.message }}
        </text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onUnmounted } from 'vue'

// 响应式数据
const lastResult = ref(null)

/**
 * 测试首次绑定银行卡
 */
const testFirstBinding = () => {
  // 清除之前的结果
  lastResult.value = null
  
  // 跳转到添加银行卡页面
  uni.navigateTo({
    url: '/pages/my/addBankCard',
    success: () => {
      console.log('跳转到首次绑定银行卡页面')
    },
    fail: (error) => {
      console.error('跳转失败:', error)
      lastResult.value = {
        success: false,
        message: '页面跳转失败'
      }
    }
  })
}

/**
 * 测试已有用户绑定银行卡
 */
const testExistingUserBinding = () => {
  // 清除之前的结果
  lastResult.value = null
  
  // 跳转到添加银行卡页面（模拟已有用户）
  uni.navigateTo({
    url: '/pages/my/addBankCard?existing=true',
    success: () => {
      console.log('跳转到已有用户绑定银行卡页面')
    },
    fail: (error) => {
      console.error('跳转失败:', error)
      lastResult.value = {
        success: false,
        message: '页面跳转失败'
      }
    }
  })
}

// 监听页面返回，获取结果
uni.$on('addBankCardResult', (result) => {
  lastResult.value = result
})

// 页面卸载时移除监听
onUnmounted(() => {
  uni.$off('addBankCardResult')
})
</script>

<style lang="scss" scoped>
.test-page {
  padding: 32rpx;
  background-color: #f7f7f7;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .test-title {
    font-size: 40rpx;
    font-weight: 600;
    color: #333333;
  }
}

.test-content {
  .test-info {
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 40rpx;
    
    .info-item {
      margin-bottom: 32rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .info-label {
        font-size: 28rpx;
        color: #333333;
        font-weight: 600;
        display: block;
        margin-bottom: 12rpx;
      }
      
      .info-text {
        font-size: 26rpx;
        color: #666666;
        line-height: 1.6;
        display: block;
        margin-bottom: 8rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  
  .test-buttons {
    margin-bottom: 40rpx;
    
    .test-btn {
      width: 100%;
      height: 88rpx;
      font-size: 32rpx;
      border-radius: 44rpx;
      border: none;
      margin-bottom: 32rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      &.primary {
        background-color: #4CAF50;
        color: #ffffff;
        
        &:active {
          background-color: #45a049;
        }
      }
      
      &.secondary {
        background-color: #007AFF;
        color: #ffffff;
        
        &:active {
          background-color: #0056b3;
        }
      }
    }
  }
  
  .test-result {
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    
    .result-title {
      font-size: 28rpx;
      color: #666666;
      display: block;
      margin-bottom: 16rpx;
    }
    
    .result-text {
      font-size: 30rpx;
      font-weight: 500;
      
      &.success {
        color: #28a745;
      }
      
      &.failed {
        color: #dc3545;
      }
    }
  }
}
</style>
