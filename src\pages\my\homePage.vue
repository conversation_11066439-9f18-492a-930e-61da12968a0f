<template>
  <view class="homePage">
    <view class="infoBox">
      <view class="avatarBox">
        <image src="@/static/image/avatar.png"></image>
      </view>
      <view class="nameBox">
        <view class="nameTitle">{{ loginInfo.name }}</view>
        <view class="phoneTitle">手机号：{{ loginInfo.phone }}</view>
      </view>
    </view>
    <view class="listPage">
      <view class="listItem" @click="toInformationPage">
        <view class="listItem_name">
          <image class="item_icon" src="@/static/image/bdxxIcon.png"></image>
          <view class="item_title">报到信息</view>
        </view>
        <image class="rightIcon" src="@/static/image/right.png"></image>
      </view>
      <view class="listItem" @click="toOrderRecordPage">
        <view class="listItem_name">
          <image class="item_icon" src="@/static/image/jfddIcon.png"></image>
          <view class="item_title">缴费订单</view>
        </view>
        <image class="rightIcon" src="@/static/image/right.png"></image>
      </view>
      <view class="listItem" @click="toBankList">
        <view class="listItem_name">
          <image class="item_icon" src="@/static/image/yhkIcon.png"></image>
          <view class="item_title">银行卡绑定/解绑</view>
        </view>
        <image class="rightIcon" src="@/static/image/right.png"></image>
      </view>
      <view class="listItem" @click="toSetting">
        <view class="listItem_name">
          <image class="item_icon" src="@/static/image/szIcon.png"></image>
          <view class="item_title"> 设置</view>
        </view>
        <image class="rightIcon" src="@/static/image/right.png"></image>
      </view>
    </view>
  </view>
</template>

<script setup>
import { useSafeStorage } from "@/hooks/useSafeStorage";

const loginInfo = useSafeStorage("checkIn-loginInfo", {});

console.log(loginInfo, "loginInfo");

const toInformationPage = () => {
  uni.navigateTo({
    url: `/pages/my/information`,
  });
};

const toOrderRecordPage = () => {
  uni.navigateTo({
    url: `/pages/my/orderRecord`,
  });
};

const toSetting = () => {
  uni.navigateTo({
    url: `/pages/my/setting`,
  });
};

const toBankList = () => {
  uni.navigateTo({
    url: `/pages/my/bankList`,
  });
};

onShow(() => {
  const loginInfo = useSafeStorage("checkIn-loginInfo", {});
  if (!loginInfo.value.reportId) {
    uni.reLaunch({
      url: "/pages/home/<USER>",
    });
  }
});

onBackPress(() => {
  uni.navigateTo({
    url: "/pages/home/<USER>",
  });
  return true;
});
</script>

<style scoped>
.homePage {
  min-height: 100vh;
  background-color: #f6f6f6;
}
.infoBox {
  height: 200rpx;
  background: linear-gradient(180deg, #e9fcf2 0%, #f4fbf6 100%);
  display: flex;
  padding-left: 34rpx;
  padding-right: 34rpx;
  align-items: center;
}
.avatarBox {
  padding-right: 20rpx;
  width: 108rpx;
  height: 108rpx;
}
.avatarBox image {
  width: 108rpx;
  height: 108rpx;
}

.nameBox {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.nameTitle {
  font-weight: 600;
  font-size: 32rpx;
  color: #202020;
  padding-bottom: 32rpx;
}

.phoneTitle {
  font-weight: 400;
  font-size: 24rpx;
  color: #696969;
}

.listPage {
  background: #ffffff;
  padding-left: 30rpx;
  padding-right: 30rpx;
}
.listItem {
  padding: 30rpx 0rpx 30rpx 0rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #e8e8e8;
}

.item_icon {
  width: 32rpx;
  height: 32rpx;
}

.rightIcon {
  width: 40rpx;
  height: 40rpx;
}

.listItem_name {
  display: flex;
  align-items: center;
}

.item_title {
  font-weight: 400;
  font-size: 30rpx;
  color: #333333;
  padding-left: 16rpx;
}
</style>
