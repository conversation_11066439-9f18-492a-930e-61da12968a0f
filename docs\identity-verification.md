# 身份验证功能文档

## 功能概述

身份验证页面提供了基于手机号和短信验证码的身份验证功能，用户可以通过输入手机号获取验证码，然后输入验证码进行身份验证。

## 页面路径

- 页面文件：`src/pages/my/identity.vue`
- 路由路径：`/pages/my/identity`
- 页面标题：身份验证

## 功能特性

### 1. 手机号输入
- 支持11位手机号输入
- 自动过滤非数字字符
- 实时验证手机号格式（以1开头的11位数字）
- 无边框设计，符合UI设计稿要求

### 2. 验证码获取
- 手机号格式正确后才能发送验证码
- 60秒倒计时防止重复发送
- 发送状态提示（发送中、发送成功、发送失败）
- 按钮状态动态变化（激活/禁用）

### 3. 验证码输入
- 支持6位数字验证码输入
- 自动过滤非数字字符
- 无边框设计

### 4. 身份验证
- 手机号和验证码都正确填写后才能提交
- 验证状态提示（验证中、验证成功、验证失败）
- 底部固定按钮设计

## API接口

### 发送验证码
- **接口地址**：`/app/enrollment/sms/otc/send`
- **请求方法**：POST
- **请求参数**：
  ```json
  {
    "phone": "手机号"
  }
  ```

### 验证验证码
- **接口地址**：`/app/enrollment/sms/otc/verify`
- **请求方法**：POST
- **请求参数**：
  ```json
  {
    "phone": "手机号",
    "smsCode": "验证码"
  }
  ```

## 技术实现

### 1. 技术栈
- Vue 3 Composition API
- UniApp框架
- SCSS样式预处理器

### 2. 核心功能
- 响应式数据管理
- 表单验证
- API请求封装
- 错误处理
- 倒计时功能
- 生命周期管理

### 3. 样式特点
- 无边框输入框设计
- 底部固定按钮
- 响应式布局
- 安全区域适配
- 动态按钮状态

## 使用方法

### 1. 页面跳转
```javascript
uni.navigateTo({
  url: '/pages/my/identity'
})
```

### 2. 测试页面
可以通过测试页面进行功能验证：
- 测试页面：`src/pages/test/identityTest.vue`
- 路由路径：`/pages/test/identityTest`

## 注意事项

1. 确保后端API接口正常可用
2. 手机号格式验证：11位数字，以1开头
3. 验证码长度：6位数字
4. 倒计时时间：60秒
5. 页面适配了安全区域，支持各种设备

## 扩展功能

可以根据业务需求进行以下扩展：
1. 添加图形验证码
2. 支持语音验证码
3. 添加多种验证方式
4. 集成第三方登录
5. 添加生物识别验证
