<template>
  <view class="order-record-page">
    <!-- z-paging组件，铺满全屏 -->
    <z-paging
      ref="pagingRef"
      v-model="orderList"
      :fixed="true"
      :auto="true"
      :default-page-size="10"
      :refresher-enabled="true"
      :refresher-threshold="80"
      :loading-more-enabled="true"
      @query="queryOrderList"
      @refresherrefresh="onRefresh"
    >
      <!-- 顶部固定区域：搜索和筛选 -->
      <template #top>
        <view class="search-filter-section">
          <!-- 搜索框 -->
          <view class="search-container">
            <uni-search-bar
              v-model="searchKeyword"
              placeholder="请输入缴费项目"
              :radius="30"
              :focus="false"
              :show-action="false"
              clearButton="always"
              cancelButton="none"
              @input="onSearchInput"
              @clear="onSearchClear"
            />
          </view>

          <!-- 筛选器 -->
          <view class="filter-container">
            <!-- 缴费类型筛选 -->
            <view class="filter-item" @click="togglePaymentTypeFilter">
              <text class="filter-label">缴费类型</text>
              <uni-icons
                :type="showPaymentTypeFilter ? 'up' : 'down'"
                size="14"
                color="#666666"
              />
            </view>

            <!-- 成功缴费时间筛选 -->
            <view class="filter-item" @click="toggleTimeRangeFilter">
              <text class="filter-label">成功缴费时间</text>
              <uni-icons
                :type="showTimeRangeFilter ? 'up' : 'down'"
                size="14"
                color="#666666"
              />
            </view>
          </view>

          <!-- 缴费类型选择器 -->
          <view v-if="showPaymentTypeFilter" class="filter-dropdown">
            <uni-data-select
              v-model="filterData.paymentType"
              :localdata="paymentTypeOptions"
              placeholder="请选择缴费类型"
              :clear="true"
              @change="onPaymentTypeChange"
            />
          </view>

          <!-- 时间范围选择器 -->
          <view v-if="showTimeRangeFilter" class="filter-dropdown">
            <uni-datetime-picker
              v-model="filterData.timeRange"
              type="daterange"
              :border="false"
              placeholder="请选择时间范围"
              :clear-icon="true"
              @change="onTimeRangeChange"
            />
          </view>
        </view>
      </template>
      <!-- 订单卡片列表 -->
      <view class="order-card-list">
        <view class="order-card" v-for="item in orderList" :key="item.id">
          <!-- 订单头部 -->
          <view class="order-header">
            <view class="order-type">
              <text class="type-icon"></text>
              <text class="type-text">{{ item.typeName }}</text>
            </view>
            <view class="order-status" :class="getStatusClass(item.status)">
              {{ getStatusText(item.status) }}
            </view>
          </view>

          <!-- 订单标题 -->
          <view class="order-title">{{ item.title }}</view>

          <!-- 订单详情 -->
          <view class="order-details">
            <view class="detail-row">
              <text class="detail-label">缴费时间:</text>
              <text class="detail-value">{{ item.paymentTime }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">缴费明细:</text>
              <text class="detail-value">{{ item.paymentDetail }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">应缴金额:</text>
              <text class="detail-value">{{ item.shouldPayAmount }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">减免金额:</text>
              <text class="detail-value">{{ item.discountAmount }}</text>
            </view>
          </view>

          <!-- 实缴金额 -->
          <view class="actual-amount">
            <text class="amount-label">实缴金额</text>
            <text class="amount-value">¥{{ item.actualAmount }}</text>
          </view>

          <!-- 操作按钮 -->
          <view class="order-actions">
            <view
              class="action-btn secondary"
              @click="cancelOrder(item)"
              v-if="item.status === 'unpaid'"
            >
              取消订单
            </view>
            <view
              class="action-btn primary"
              @click="payOrder(item)"
              v-if="item.status === 'unpaid'"
            >
              立即支付
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";

// 响应式数据
const searchKeyword = ref("");
const orderList = ref([]);
const pagingRef = ref(null);

// 筛选器展开状态
const showPaymentTypeFilter = ref(false);
const showTimeRangeFilter = ref(false);

// 筛选数据
const filterData = reactive({
  paymentType: "",
  timeRange: [],
});

// 缴费类型选项
const paymentTypeOptions = ref([
  { value: "", text: "全部" },
  { value: "tuition", text: "学费" },
  { value: "accommodation", text: "住宿费" },
  { value: "textbook", text: "教材费" },
  { value: "insurance", text: "保险费" },
  { value: "other", text: "其他费用" },
]);

// 查询订单列表
const queryOrderList = async (pageNo, pageSize) => {
  try {
    const params = {
      pageNo,
      pageSize,
      keyword: searchKeyword.value,
      paymentType: filterData.paymentType,
      startTime: filterData.timeRange[0] || "",
      endTime: filterData.timeRange[1] || "",
    };

    // 模拟API调用 - 实际项目中替换为真实接口
    const response = await mockOrderListAPI(params);

    // 使用z-paging的complete方法完成数据加载
    pagingRef.value.complete(response.data.list);
  } catch (error) {
    console.error("查询订单列表失败:", error);
    pagingRef.value.complete([]);
  }
};

// 模拟订单列表API
const mockOrderListAPI = (params) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = generateMockOrderData(params.pageNo, params.pageSize);
      resolve({
        data: {
          list: mockData,
          total: 50,
        },
      });
    }, 500);
  });
};

// 生成模拟订单数据
const generateMockOrderData = (pageNo, pageSize) => {
  const data = [];
  const startIndex = (pageNo - 1) * pageSize;

  for (let i = 0; i < pageSize; i++) {
    const index = startIndex + i + 1;
    const isFirstType = index % 2 === 1;
    data.push({
      id: `order_${index}`,
      type: isFirstType ? "tuition" : "exam",
      typeName: isFirstType ? "学会费" : "考试会费",
      title: isFirstType ? "2024下学年学会费" : "2024下学年学会费",
      paymentTime: "2024.05.12-2025.06.12",
      paymentDetail: "解决订单",
      shouldPayAmount: isFirstType ? "1000.00" : "1200.00",
      discountAmount: isFirstType ? "200.00" : "1000.00",
      actualAmount: isFirstType ? "800.00" : "199.9",
      status: index % 3 === 0 ? "paid" : "unpaid",
    });
  }

  return data;
};

// 切换缴费类型筛选器
const togglePaymentTypeFilter = () => {
  showPaymentTypeFilter.value = !showPaymentTypeFilter.value;
  // 关闭其他筛选器
  if (showPaymentTypeFilter.value) {
    showTimeRangeFilter.value = false;
  }
};

// 切换时间范围筛选器
const toggleTimeRangeFilter = () => {
  showTimeRangeFilter.value = !showTimeRangeFilter.value;
  // 关闭其他筛选器
  if (showTimeRangeFilter.value) {
    showPaymentTypeFilter.value = false;
  }
};

// 搜索输入处理
const onSearchInput = (value) => {
  searchKeyword.value = value;
  // 延迟搜索，避免频繁请求
  clearTimeout(searchTimer);
  searchTimer = setTimeout(() => {
    refreshData();
  }, 500);
};

// 搜索清除处理
const onSearchClear = () => {
  searchKeyword.value = "";
  refreshData();
};

// 缴费类型变化处理
const onPaymentTypeChange = (value) => {
  filterData.paymentType = value;
  showPaymentTypeFilter.value = false; // 选择后自动收起
  refreshData();
};

// 时间范围变化处理
const onTimeRangeChange = (value) => {
  filterData.timeRange = value;
  showTimeRangeFilter.value = false; // 选择后自动收起
  refreshData();
};

// 下拉刷新处理
const onRefresh = () => {
  refreshData();
};

// 刷新数据
const refreshData = () => {
  if (pagingRef.value) {
    pagingRef.value.reload();
  }
};

// 获取状态样式类
const getStatusClass = (status) => {
  return status === "paid" ? "status-paid" : "status-unpaid";
};

// 获取状态文本
const getStatusText = (status) => {
  return status === "paid" ? "已支付" : "待支付";
};

// 取消订单
const cancelOrder = (order) => {
  uni.showModal({
    title: "确认取消",
    content: "确定要取消这个订单吗？",
    success: (res) => {
      if (res.confirm) {
        console.log("取消订单:", order);
        // 实际项目中调用取消订单接口
        uni.showToast({
          title: "订单已取消",
          icon: "success",
        });
        // 刷新列表
        refreshData();
      }
    },
  });
};

// 支付订单
const payOrder = (order) => {
  console.log("支付订单:", order);
  // 实际项目中跳转到支付页面
  uni.navigateTo({
    url: `/pages/checkIn/payment?orderId=${order.id}`,
  });
};

// 搜索定时器
let searchTimer = null;

// 页面挂载时的处理
onMounted(() => {
  // 页面初始化逻辑
});
</script>

<style scoped>
/* 页面容器 */
.order-record-page {
  min-height: 100vh;
  background-color: #f9faf9;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 搜索和筛选区域 */
.search-filter-section {
  background-color: #ffffff;

  border-bottom: 1rpx solid #f0f0f0;
}

/* 搜索容器 */
.search-container {
  border-bottom: 2rpx solid #ebebeb;
  padding-left: 32rpx;
  padding-right: 32rpx;
}

/* 筛选容器 */
.filter-container {
  display: flex;

  justify-content: space-between;

  padding: 32rpx 54rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8rpx;

  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-label {
  font-size: 28rpx;
  color: #333333;
}

/* 筛选下拉框 */
.filter-dropdown {
  padding: 16rpx;
  background-color: #ffffff;
}

.order-card-list {
  margin: 32rpx;
}

.order-card-list .order-card:first-child {
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
}

.order-card-list .order-card:last-child {
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
}

/* 订单卡片 */
.order-card {
  background: #ffffff;
  padding: 32rpx;
  border-bottom: 1rpx solid #ebebeb;
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.order-type {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.type-icon {
  width: 6rpx;
  height: 24rpx;
  background: #00b781;
  border-radius: 3rpx;
}

.type-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-paid {
  background-color: #e8f5e8;
  color: #00b781;
}

.status-unpaid {
  background-color: #fff7e6;
  color: #ff8c00;
}

/* 订单标题 */
.order-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
}

/* 订单详情 */
.order-details {
  margin-bottom: 32rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 28rpx;
  color: #666666;
}

.detail-value {
  font-size: 28rpx;
  color: #333333;
  text-align: right;
}

/* 实缴金额 */
.actual-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-top: 1rpx solid #f0f0f0;
  margin-bottom: 32rpx;
}

.amount-label {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.amount-value {
  font-size: 36rpx;
  color: #ff4d4f;
  font-weight: 600;
}

/* 操作按钮 */
.order-actions {
  display: flex;
  gap: 24rpx;
  justify-content: flex-end;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background-color: #f5f5f5;
  color: #666666;
  border: 1rpx solid #d9d9d9;
}

.action-btn.secondary:active {
  background-color: #e6e6e6;
}

.action-btn.primary {
  background-color: #00b781;
  color: #ffffff;
  border: 1rpx solid #00b781;
}

.action-btn.primary:active {
  background-color: #009a6d;
}

:deep(.uni-data-select) {
  border: 1rpx solid #e6e6e6;
  border-radius: 8rpx;
  background-color: #ffffff;
}

:deep(.uni-select__input-text) {
  font-size: 28rpx;
  color: #333333;
}

:deep(.uni-select__input-placeholder) {
  color: #999999;
  font-size: 28rpx;
}

:deep(.uni-datetime-picker) {
  border: 1rpx solid #e6e6e6;
  border-radius: 8rpx;
  background-color: #ffffff;
}

:deep(.uni-datetime-picker .uni-datetime-picker-view) {
  border: none;
  background-color: transparent;
}

:deep(.uni-datetime-picker .uni-datetime-picker-text) {
  font-size: 28rpx;
  color: #333333;
}

:deep(.uni-datetime-picker .uni-datetime-picker-placeholder) {
  color: #999999;
  font-size: 28rpx;
}

/* z-paging组件样式覆盖 */
:deep(.zp-paging-container) {
  background-color: #f9faf9;
}

:deep(.zp-paging-container-content) {
  background-color: #f9faf9;
}
</style>
