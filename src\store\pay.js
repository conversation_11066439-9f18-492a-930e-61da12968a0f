import { defineStore } from "pinia";

const usePayStore = defineStore("pay", {
  state: () => {
    return {
      smsCheckPrepay: {},
    };
  },

  getters: {
    getSmsCheckPrepay(state) {
      return state.smsCheckPrepay;
    },
  },

  actions: {
    resetPaty() {
      this.smsCheckPrepay = {};
    },
    setSmsCheckPrepay(smsCheckPrepay) {
      this.smsCheckPrepay = smsCheckPrepay;
    },
  },

  // 配置持久化存储
  persist: {
    key: "pay",
    paths: ["smsCheckPrepay"], // 指定需要持久化的状态
  },
});

export default usePayStore;
