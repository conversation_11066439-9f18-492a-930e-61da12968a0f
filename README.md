# 报名-移动端

新乡职校报名系统-移动端，[pc端]()。

## 安装
```bash
  pnpm install
```

## 运行
```bash
  npm run dev:h5
```

## 部署/构建
```bash
  npm run build:h5
```
|  环境 | 帐号     | 密码                |
| :-------- | :------- | :------------------------- |
| 测试环境 | string | 123456 |
| 生产环境 | string | 123456 |

`构建地址`：[]()

## 相关文档
- [代码地址]()

- [API 文档]()

- [原型 地址](https://lanhuapp.com/web/#/item/project/product?tid=3aae0c0e-5180-4f02-817b-fc646005e0a7&pid=465bba9a-a616-4a3c-8483-d4dfdce578f2&versionId=2caa9c41-81d4-4f60-9864-af7830889255&docId=8e445135-64c6-467c-a593-ac11461e490d&docType=axure&pageId=c12302af22164997915ef54fc09ae74a&image_id=8e445135-64c6-467c-a593-ac11461e490d&parentId=d8da62da-c356-4965-a41c-07ba7deb657e)

- [设计稿 地址](https://lanhuapp.com/web/#/item/project/stage?pid=465bba9a-a616-4a3c-8483-d4dfdce578f2&image_id=472a0c10-1fc5-440a-874e-1b02ffc7d536&tid=3aae0c0e-5180-4f02-817b-fc646005e0a7)

- [Tapd 需求](https://www.tapd.cn/tapd_fe/32711522/story/detail/1132711522001014873)
  -  [任务](https://www.tapd.cn/tapd_fe/32711522/iteration/card/1132711522001000312?q=4c4e7837d81699ba984e672a081408e6)

## 技术栈

####  1.业务流程描述

####  3.技术选型

####  2.页面结构

- home 首页
- login 登录（预录取通知书）
- guide 招生须知
- major 专业
- major-detail 专业详情
- personal-info 个人信息
- upload-annex 上传附件
- family-info 家庭信息
- register 报名信息
- register-success 报名成功
- notice 录取通知书
- order 订单
- payment 支付